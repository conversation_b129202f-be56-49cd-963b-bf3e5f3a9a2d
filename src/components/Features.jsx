import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import {
  Zap,
  Shield,
  BarChart3,
  Users,
  Clock,
  Smartphone,
  Cloud,
  Lock,
  Headphones,
  ArrowRight,
  CheckCircle,
  Play
} from 'lucide-react'

const Features = () => {
  const [activeFeature, setActiveFeature] = useState(0)
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3
      }
    }
  }

  const itemVariants = {
    hidden: { x: -50, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12
      }
    }
  }

  const lineVariants = {
    hidden: { pathLength: 0 },
    visible: {
      pathLength: 1,
      transition: {
        duration: 2,
        ease: "easeInOut"
      }
    }
  }

  const features = [
    {
      icon: Zap,
      title: "Lightning Fast Performance",
      description: "Experience blazing fast performance with our optimized infrastructure and cutting-edge technology stack.",
      details: "Built on modern cloud architecture with global CDN distribution, ensuring sub-100ms response times worldwide.",
      color: "from-yellow-400 to-orange-500",
      bgColor: "from-yellow-50 to-orange-50",
      year: "2024",
      status: "Active"
    },
    {
      icon: Shield,
      title: "Enterprise Security",
      description: "Bank-level security with end-to-end encryption, ensuring your data is always protected and compliant.",
      details: "SOC 2 Type II certified with 256-bit AES encryption, multi-factor authentication, and regular security audits.",
      color: "from-green-400 to-blue-500",
      bgColor: "from-green-50 to-blue-50",
      year: "2024",
      status: "Active"
    },
    {
      icon: BarChart3,
      title: "Advanced Analytics",
      description: "Get deep insights with real-time analytics and customizable dashboards for data-driven decisions.",
      details: "Machine learning-powered insights with predictive analytics, custom reporting, and automated alerts.",
      color: "from-purple-400 to-pink-500",
      bgColor: "from-purple-50 to-pink-50",
      year: "2024",
      status: "Active"
    },
    {
      icon: Users,
      title: "Team Collaboration",
      description: "Seamlessly collaborate with your team using our intuitive tools and real-time synchronization.",
      details: "Real-time editing, video conferencing integration, task management, and team communication tools.",
      color: "from-blue-400 to-indigo-500",
      bgColor: "from-blue-50 to-indigo-50",
      year: "2024",
      status: "Active"
    },
    {
      icon: Cloud,
      title: "Cloud Integration",
      description: "Seamlessly integrate with popular cloud services and sync your data across all platforms.",
      details: "Native integrations with AWS, Google Cloud, Azure, and 100+ popular business applications.",
      color: "from-indigo-400 to-purple-500",
      bgColor: "from-indigo-50 to-purple-50",
      year: "2024",
      status: "Active"
    },
    {
      icon: Smartphone,
      title: "Mobile Optimized",
      description: "Access your workspace from anywhere with our fully responsive design and native mobile apps.",
      details: "Progressive Web App with offline capabilities, push notifications, and native iOS/Android apps.",
      color: "from-teal-400 to-cyan-500",
      bgColor: "from-teal-50 to-cyan-50",
      year: "2024",
      status: "Coming Soon"
    }
  ]

  return (
    <section id="features" className="py-20 bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234F46E5' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="text-center mb-20"
        >
          <motion.div
            variants={itemVariants}
            className="inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 text-sm font-medium mb-8 border border-blue-200"
          >
            <Zap className="w-4 h-4 mr-2" />
            Platform Features
          </motion.div>

          <motion.h2
            variants={itemVariants}
            className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight"
          >
            Built for the
            <span className="block bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              future of work
            </span>
          </motion.h2>

          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed"
          >
            Discover how our cutting-edge features transform the way teams collaborate,
            analyze data, and scale their operations in the digital age.
          </motion.p>
        </motion.div>

        {/* Timeline Layout */}
        <div className="relative">
          {/* Main Timeline Line */}
          <div className="hidden lg:block absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-gradient-to-b from-blue-200 via-purple-200 to-blue-200 rounded-full">
            <motion.div
              className="absolute inset-0 bg-gradient-to-b from-blue-500 via-purple-500 to-blue-500 rounded-full"
              variants={lineVariants}
              initial="hidden"
              animate={inView ? "visible" : "hidden"}
            />
          </div>

          {/* Features */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={inView ? "visible" : "hidden"}
            className="space-y-16 lg:space-y-24"
          >
            {features.map((feature, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className={`relative flex flex-col lg:flex-row items-center ${
                  index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'
                }`}
                onHoverStart={() => setActiveFeature(index)}
              >
                {/* Timeline Node */}
                <div className="hidden lg:block absolute left-1/2 transform -translate-x-1/2 z-20">
                  <motion.div
                    className={`w-6 h-6 rounded-full bg-gradient-to-r ${feature.color} border-4 border-white shadow-lg`}
                    whileHover={{ scale: 1.5 }}
                    animate={{
                      scale: activeFeature === index ? 1.3 : 1,
                      boxShadow: activeFeature === index ? "0 0 20px rgba(59, 130, 246, 0.5)" : "0 4px 6px rgba(0, 0, 0, 0.1)"
                    }}
                    transition={{ type: "spring", stiffness: 300, damping: 20 }}
                  />
                </div>

                {/* Content Card */}
                <motion.div
                  className={`w-full lg:w-5/12 ${
                    index % 2 === 0 ? 'lg:pr-16' : 'lg:pl-16'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  <div className="relative group">
                    {/* Year Badge */}
                    <motion.div
                      className={`absolute -top-4 ${
                        index % 2 === 0 ? 'right-4' : 'left-4'
                      } bg-gradient-to-r ${feature.color} text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg`}
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.2 }}
                    >
                      {feature.year}
                    </motion.div>

                    {/* Status Badge */}
                    <motion.div
                      className={`absolute -top-4 ${
                        index % 2 === 0 ? 'left-4' : 'right-4'
                      } flex items-center`}
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.2 + 0.1 }}
                    >
                      <div className={`flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                        feature.status === 'Active'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-orange-100 text-orange-800'
                      }`}>
                        {feature.status === 'Active' ? (
                          <CheckCircle className="w-3 h-3 mr-1" />
                        ) : (
                          <Clock className="w-3 h-3 mr-1" />
                        )}
                        {feature.status}
                      </div>
                    </motion.div>

                    {/* Main Card */}
                    <div className={`relative p-8 bg-white rounded-2xl border border-gray-200 shadow-lg hover:shadow-2xl transition-all duration-500 group-hover:border-gray-300 overflow-hidden`}>
                      {/* Gradient Background */}
                      <motion.div
                        className={`absolute inset-0 bg-gradient-to-br ${feature.bgColor} opacity-0 group-hover:opacity-100 transition-opacity duration-500`}
                        initial={false}
                      />

                      {/* Animated Border */}
                      <motion.div
                        className={`absolute inset-0 bg-gradient-to-r ${feature.color} opacity-0 group-hover:opacity-20 rounded-2xl`}
                        initial={false}
                        whileHover={{ opacity: 0.1 }}
                      />

                      <div className="relative z-10">
                        {/* Icon */}
                        <motion.div
                          className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-r ${feature.color} mb-6 shadow-lg`}
                          whileHover={{ scale: 1.1, rotate: 5 }}
                          transition={{ type: "spring", stiffness: 400, damping: 10 }}
                        >
                          <feature.icon className="w-8 h-8 text-white" />
                        </motion.div>

                        {/* Content */}
                        <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">
                          {feature.title}
                        </h3>

                        <p className="text-gray-600 mb-4 leading-relaxed">
                          {feature.description}
                        </p>

                        <p className="text-sm text-gray-500 mb-6 leading-relaxed">
                          {feature.details}
                        </p>

                        {/* CTA */}
                        <motion.button
                          className="flex items-center text-blue-600 font-semibold hover:text-blue-700 transition-colors group/btn"
                          whileHover={{ x: 5 }}
                        >
                          <Play className="w-4 h-4 mr-2" />
                          Learn More
                          <ArrowRight className="w-4 h-4 ml-2 group-hover/btn:translate-x-1 transition-transform" />
                        </motion.button>
                      </div>

                      {/* Connecting Line to Timeline */}
                      <div className={`hidden lg:block absolute top-1/2 ${
                        index % 2 === 0 ? '-right-8' : '-left-8'
                      } w-8 h-0.5 bg-gradient-to-r ${feature.color} opacity-30 group-hover:opacity-100 transition-opacity duration-500`} />
                    </div>
                  </div>
                </motion.div>

                {/* Visual Element */}
                <motion.div
                  className={`w-full lg:w-5/12 mt-8 lg:mt-0 ${
                    index % 2 === 0 ? 'lg:pl-16' : 'lg:pr-16'
                  }`}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.2 + 0.3 }}
                >
                  <div className={`relative h-64 bg-gradient-to-br ${feature.bgColor} rounded-2xl border border-gray-200 overflow-hidden group`}>
                    {/* Animated Background Pattern */}
                    <motion.div
                      className="absolute inset-0 opacity-10"
                      animate={{
                        backgroundPosition: ["0% 0%", "100% 100%"],
                      }}
                      transition={{
                        duration: 20,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                      style={{
                        backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%234F46E5' fill-opacity='0.1'%3E%3Cpath d='M20 20c0-11.046-8.954-20-20-20v20h20z'/%3E%3C/g%3E%3C/svg%3E")`,
                        backgroundSize: "40px 40px"
                      }}
                    />

                    {/* Central Icon */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <motion.div
                        className={`w-20 h-20 bg-gradient-to-r ${feature.color} rounded-2xl flex items-center justify-center shadow-2xl`}
                        animate={{
                          y: [-5, 5, -5],
                          rotate: [0, 2, -2, 0]
                        }}
                        transition={{
                          duration: 4,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      >
                        <feature.icon className="w-10 h-10 text-white" />
                      </motion.div>
                    </div>

                    {/* Floating Elements */}
                    {[...Array(3)].map((_, i) => (
                      <motion.div
                        key={i}
                        className={`absolute w-3 h-3 bg-gradient-to-r ${feature.color} rounded-full opacity-60`}
                        style={{
                          left: `${20 + i * 25}%`,
                          top: `${30 + i * 15}%`,
                        }}
                        animate={{
                          y: [-10, 10, -10],
                          opacity: [0.3, 0.8, 0.3],
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          delay: i * 0.5,
                          ease: "easeInOut"
                        }}
                      />
                    ))}
                  </div>
                </motion.div>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Bottom CTA */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="text-center mt-20"
        >
          <motion.button
            className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-10 py-4 rounded-2xl font-semibold text-lg hover:shadow-2xl transition-all duration-300 border border-blue-500"
            whileHover={{
              scale: 1.05,
              boxShadow: "0 25px 50px rgba(59, 130, 246, 0.3)"
            }}
            whileTap={{ scale: 0.95 }}
          >
            <span className="flex items-center">
              Explore All Features
              <ArrowRight className="ml-2 w-5 h-5" />
            </span>
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}

export default Features
