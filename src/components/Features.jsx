import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import {
  Zap,
  Shield,
  BarChart3,
  Users,
  Cloud,
  Smartphone,
  ArrowRight,
  Play
} from 'lucide-react'

const Features = () => {
  const [activeFeature, setActiveFeature] = useState(0)
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  const features = [
    {
      icon: Zap,
      title: "Lightning Fast Performance",
      description: "Experience blazing fast performance with our optimized infrastructure and cutting-edge technology stack.",
      color: "from-yellow-400 to-orange-500"
    },
    {
      icon: Shield,
      title: "Enterprise Security",
      description: "Bank-level security with end-to-end encryption, ensuring your data is always protected and compliant.",
      color: "from-green-400 to-blue-500"
    },
    {
      icon: BarChart3,
      title: "Advanced Analytics",
      description: "Get deep insights with real-time analytics and customizable dashboards for data-driven decisions.",
      color: "from-purple-400 to-pink-500"
    },
    {
      icon: Users,
      title: "Team Collaboration",
      description: "Seamlessly collaborate with your team using our intuitive tools and real-time synchronization.",
      color: "from-blue-400 to-indigo-500"
    },
    {
      icon: Cloud,
      title: "Cloud Integration",
      description: "Seamlessly integrate with popular cloud services and sync your data across all platforms.",
      color: "from-indigo-400 to-purple-500"
    },
    {
      icon: Smartphone,
      title: "Mobile Experience",
      description: "Access your workspace from anywhere with our fully responsive design and native mobile apps.",
      color: "from-teal-400 to-cyan-500"
    }
  ]

  return (
    <section id="features" className="bg-white">
      <div className="flex flex-col lg:flex-row min-h-screen">
        {/* Left Sidebar - Fixed on desktop */}
        <div className="lg:w-2/5 lg:fixed lg:left-0 lg:top-0 lg:h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white p-8 lg:p-12 flex flex-col justify-center">
          <div className="max-w-md">
            {/* Header */}
            <div className="mb-12">
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/10 text-blue-200 text-sm font-medium mb-6">
                ✨ Platform Features
              </div>

              <h2 className="text-4xl lg:text-5xl font-bold mb-6 leading-tight">
                Built for the
                <span className="block bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  future of work
                </span>
              </h2>

              <p className="text-lg text-blue-100 leading-relaxed">
                Discover how our cutting-edge features transform the way teams collaborate and scale.
              </p>
            </div>

            {/* Feature Navigation */}
            <div className="space-y-3">
              {features.map((feature, index) => (
                <button
                  key={index}
                  className={`w-full text-left p-4 rounded-xl transition-all duration-300 ${
                    activeFeature === index
                      ? 'bg-white/20 border border-white/30'
                      : 'hover:bg-white/10 border border-transparent'
                  }`}
                  onClick={() => setActiveFeature(index)}
                >
                  <div className="flex items-center">
                    <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${feature.color} flex items-center justify-center mr-3`}>
                      <feature.icon className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-white text-sm">
                        {feature.title}
                      </h3>
                    </div>
                  </div>
                </button>
              ))}
            </div>

            {/* Stats */}
            <div className="mt-12 pt-8 border-t border-white/20">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-xl font-bold text-white">99.9%</div>
                  <div className="text-xs text-blue-200">Uptime</div>
                </div>
                <div>
                  <div className="text-xl font-bold text-white">100+</div>
                  <div className="text-xs text-blue-200">Integrations</div>
                </div>
                <div>
                  <div className="text-xl font-bold text-white">10M+</div>
                  <div className="text-xs text-blue-200">Users</div>
                </div>
                <div>
                  <div className="text-xl font-bold text-white">24/7</div>
                  <div className="text-xs text-blue-200">Support</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Content - Scrollable */}
        <div className="lg:w-3/5 lg:ml-auto bg-gray-50 min-h-screen">
          <div className="p-8 lg:p-16">
            {/* Current Feature Display */}
            <div className="max-w-3xl">
              <motion.div
                key={activeFeature}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="mb-12"
              >
                <div className="flex items-center mb-8">
                  <div className={`w-20 h-20 rounded-2xl bg-gradient-to-r ${features[activeFeature].color} flex items-center justify-center mr-6 shadow-xl`}>
                    <features[activeFeature].icon className="w-10 h-10 text-white" />
                  </div>
                  <div>
                    <h3 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-2">
                      {features[activeFeature].title}
                    </h3>
                  </div>
                </div>

                <p className="text-xl text-gray-600 leading-relaxed mb-8">
                  {features[activeFeature].description}
                </p>
              </motion.div>

              {/* Feature Demo Area */}
              <motion.div
                key={`demo-${activeFeature}`}
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="relative h-96 bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl border border-gray-300 overflow-hidden shadow-xl mb-12"
              >
                {/* Animated Background */}
                <div className="absolute inset-0 opacity-10">
                  <motion.div
                    animate={{
                      backgroundPosition: ["0% 0%", "100% 100%"],
                    }}
                    transition={{
                      duration: 20,
                      repeat: Infinity,
                      ease: "linear"
                    }}
                    style={{
                      backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%234F46E5' fill-opacity='0.1'%3E%3Cpath d='M20 20c0-11.046-8.954-20-20-20v20h20z'/%3E%3C/g%3E%3C/svg%3E")`,
                      backgroundSize: "40px 40px"
                    }}
                  />
                </div>

                {/* Central Feature Icon */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <motion.div
                    className={`w-40 h-40 bg-gradient-to-r ${features[activeFeature].color} rounded-3xl flex items-center justify-center shadow-2xl`}
                    animate={{
                      y: [-10, 10, -10],
                      rotate: [0, 3, -3, 0],
                      scale: [1, 1.05, 1]
                    }}
                    transition={{
                      duration: 6,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <features[activeFeature].icon className="w-20 h-20 text-white" />
                  </motion.div>
                </div>

                {/* Floating Elements */}
                {[...Array(8)].map((_, i) => (
                  <motion.div
                    key={i}
                    className={`absolute w-6 h-6 bg-gradient-to-r ${features[activeFeature].color} rounded-full opacity-30`}
                    style={{
                      left: `${10 + (i % 4) * 25}%`,
                      top: `${15 + Math.floor(i / 4) * 35}%`,
                    }}
                    animate={{
                      y: [-20, 20, -20],
                      opacity: [0.2, 0.6, 0.2],
                      scale: [0.8, 1.2, 0.8]
                    }}
                    transition={{
                      duration: 5,
                      repeat: Infinity,
                      delay: i * 0.4,
                      ease: "easeInOut"
                    }}
                  />
                ))}

                {/* Interactive Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-8">
                  <button className="bg-white/90 backdrop-blur-sm text-gray-900 px-8 py-3 rounded-xl font-semibold shadow-lg hover:bg-white transition-colors flex items-center">
                    <Play className="w-5 h-5 mr-2" />
                    Try Interactive Demo
                  </button>
                </div>
              </motion.div>

              {/* Action Buttons */}
              <motion.div
                key={`buttons-${activeFeature}`}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="flex flex-col sm:flex-row gap-4"
              >
                <button className={`flex-1 bg-gradient-to-r ${features[activeFeature].color} text-white px-8 py-4 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center hover:scale-105`}>
                  <Play className="w-5 h-5 mr-2" />
                  Learn More
                </button>

                <button className="flex-1 bg-white text-gray-900 px-8 py-4 rounded-xl font-semibold border border-gray-200 hover:border-gray-300 shadow-sm hover:shadow-md transition-all duration-300 flex items-center justify-center hover:scale-105">
                  <ArrowRight className="w-5 h-5 mr-2" />
                  View Documentation
                </button>
              </motion.div>

              {/* Bottom CTA */}
              <div className="mt-20 text-center">
                <h4 className="text-3xl font-bold text-gray-900 mb-6">
                  Ready to get started?
                </h4>
                <p className="text-lg text-gray-600 mb-8">
                  Join thousands of teams already using our platform.
                </p>
                <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-10 py-4 rounded-xl font-semibold text-lg shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                  Start Free Trial
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Features
