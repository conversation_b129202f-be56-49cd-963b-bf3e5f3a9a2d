import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import {
  Zap,
  Shield,
  BarChart3,
  Users,
  Clock,
  Smartphone,
  Cloud,
  Lock,
  Headphones,
  ArrowRight,
  CheckCircle,
  Play,
  ChevronRight,
  Star
} from 'lucide-react'

const Features = () => {
  const [activeFeature, setActiveFeature] = useState(0)
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  // Track scroll position to update active feature
  useEffect(() => {
    const handleScroll = () => {
      const featureElements = document.querySelectorAll('[data-feature-index]')
      const scrollPosition = window.scrollY + window.innerHeight / 2

      featureElements.forEach((element, index) => {
        const rect = element.getBoundingClientRect()
        const elementTop = rect.top + window.scrollY
        const elementBottom = elementTop + rect.height

        if (scrollPosition >= elementTop && scrollPosition <= elementBottom) {
          setActiveFeature(index)
        }
      })
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3
      }
    }
  }

  const itemVariants = {
    hidden: { x: -50, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12
      }
    }
  }

  const lineVariants = {
    hidden: { pathLength: 0 },
    visible: {
      pathLength: 1,
      transition: {
        duration: 2,
        ease: "easeInOut"
      }
    }
  }

  const features = [
    {
      icon: Zap,
      title: "Lightning Fast Performance",
      description: "Experience blazing fast performance with our optimized infrastructure and cutting-edge technology stack.",
      details: "Built on modern cloud architecture with global CDN distribution, ensuring sub-100ms response times worldwide.",
      metrics: ["< 100ms", "99.9% Uptime", "Global CDN"],
      color: "from-yellow-400 to-orange-500",
      bgColor: "from-yellow-50 to-orange-50",
      category: "Performance"
    },
    {
      icon: Shield,
      title: "Enterprise Security",
      description: "Bank-level security with end-to-end encryption, ensuring your data is always protected and compliant.",
      details: "SOC 2 Type II certified with 256-bit AES encryption, multi-factor authentication, and regular security audits.",
      metrics: ["SOC 2 Type II", "256-bit AES", "Zero Breaches"],
      color: "from-green-400 to-blue-500",
      bgColor: "from-green-50 to-blue-50",
      category: "Security"
    },
    {
      icon: BarChart3,
      title: "Advanced Analytics",
      description: "Get deep insights with real-time analytics and customizable dashboards for data-driven decisions.",
      details: "Machine learning-powered insights with predictive analytics, custom reporting, and automated alerts.",
      metrics: ["Real-time Data", "ML Insights", "Custom Reports"],
      color: "from-purple-400 to-pink-500",
      bgColor: "from-purple-50 to-pink-50",
      category: "Analytics"
    },
    {
      icon: Users,
      title: "Team Collaboration",
      description: "Seamlessly collaborate with your team using our intuitive tools and real-time synchronization.",
      details: "Real-time editing, video conferencing integration, task management, and team communication tools.",
      metrics: ["Real-time Sync", "Video Calls", "Task Management"],
      color: "from-blue-400 to-indigo-500",
      bgColor: "from-blue-50 to-indigo-50",
      category: "Collaboration"
    },
    {
      icon: Cloud,
      title: "Cloud Integration",
      description: "Seamlessly integrate with popular cloud services and sync your data across all platforms.",
      details: "Native integrations with AWS, Google Cloud, Azure, and 100+ popular business applications.",
      metrics: ["100+ Integrations", "Multi-cloud", "Auto Sync"],
      color: "from-indigo-400 to-purple-500",
      bgColor: "from-indigo-50 to-purple-50",
      category: "Integration"
    },
    {
      icon: Smartphone,
      title: "Mobile Experience",
      description: "Access your workspace from anywhere with our fully responsive design and native mobile apps.",
      details: "Progressive Web App with offline capabilities, push notifications, and native iOS/Android apps.",
      metrics: ["iOS & Android", "Offline Mode", "Push Notifications"],
      color: "from-teal-400 to-cyan-500",
      bgColor: "from-teal-50 to-cyan-50",
      category: "Mobile"
    }
  ]

  return (
    <section id="features" className="min-h-screen bg-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234F46E5' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="flex min-h-screen">
        {/* Left Sidebar - Fixed */}
        <div className="w-full lg:w-2/5 lg:fixed lg:left-0 lg:top-0 lg:h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white flex flex-col justify-center p-8 lg:p-12 relative z-10">
          {/* Animated Background */}
          <div className="absolute inset-0 opacity-10">
            <motion.div
              className="absolute inset-0"
              animate={{
                backgroundPosition: ["0% 0%", "100% 100%"],
              }}
              transition={{
                duration: 20,
                repeat: Infinity,
                ease: "linear"
              }}
              style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M20 20c0-11.046-8.954-20-20-20v20h20z'/%3E%3C/g%3E%3C/svg%3E")`,
                backgroundSize: "40px 40px"
              }}
            />
          </div>

          <div className="relative z-10">
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm text-blue-200 text-sm font-medium mb-6 border border-white/20">
                <Star className="w-4 h-4 mr-2" />
                Platform Features
              </div>

              <h2 className="text-4xl lg:text-5xl font-bold mb-6 leading-tight">
                Built for the
                <span className="block bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  future of work
                </span>
              </h2>

              <p className="text-lg text-blue-100 leading-relaxed mb-8">
                Discover how our cutting-edge features transform the way teams collaborate,
                analyze data, and scale their operations.
              </p>
            </motion.div>

            {/* Feature Navigation */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="space-y-4"
            >
              {features.map((feature, index) => (
                <motion.button
                  key={index}
                  className={`w-full text-left p-4 rounded-xl transition-all duration-300 group ${
                    activeFeature === index
                      ? 'bg-white/20 backdrop-blur-sm border border-white/30'
                      : 'hover:bg-white/10 border border-transparent'
                  }`}
                  onClick={() => {
                    setActiveFeature(index)
                    const element = document.querySelector(`[data-feature-index="${index}"]`)
                    if (element) {
                      element.scrollIntoView({ behavior: 'smooth', block: 'center' })
                    }
                  }}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-center">
                    <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${feature.color} flex items-center justify-center mr-4 group-hover:scale-110 transition-transform`}>
                      <feature.icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h3 className="font-semibold text-white group-hover:text-blue-200 transition-colors">
                          {feature.title}
                        </h3>
                        <ChevronRight className={`w-5 h-5 text-white/60 transition-all ${
                          activeFeature === index ? 'rotate-90 text-white' : 'group-hover:translate-x-1'
                        }`} />
                      </div>
                      <p className="text-sm text-blue-200 mt-1">{feature.category}</p>
                    </div>
                  </div>

                  {/* Active indicator */}
                  <motion.div
                    className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-400 to-purple-400 rounded-r"
                    initial={false}
                    animate={{
                      opacity: activeFeature === index ? 1 : 0,
                      scaleY: activeFeature === index ? 1 : 0
                    }}
                    transition={{ duration: 0.3 }}
                  />
                </motion.button>
              ))}
            </motion.div>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="mt-12 pt-8 border-t border-white/20"
            >
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <div className="text-2xl font-bold text-white">99.9%</div>
                  <div className="text-sm text-blue-200">Uptime</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">100+</div>
                  <div className="text-sm text-blue-200">Integrations</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">10M+</div>
                  <div className="text-sm text-blue-200">Users</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">24/7</div>
                  <div className="text-sm text-blue-200">Support</div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Right Content - Scrollable */}
        <div className="w-full lg:w-3/5 lg:ml-auto bg-gray-50 min-h-screen">
          <div className="p-8 lg:p-16 space-y-32">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                data-feature-index={index}
                className="min-h-screen flex items-center"
                initial={{ opacity: 0, y: 100 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true, margin: "-20%" }}
              >
                <div className="w-full max-w-2xl">
                  {/* Feature Header */}
                  <motion.div
                    className="mb-12"
                    initial={{ opacity: 0, x: 50 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    viewport={{ once: true }}
                  >
                    <div className="flex items-center mb-6">
                      <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${feature.color} flex items-center justify-center mr-6 shadow-xl`}>
                        <feature.icon className="w-8 h-8 text-white" />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-blue-600 mb-1">{feature.category}</div>
                        <h3 className="text-3xl lg:text-4xl font-bold text-gray-900">{feature.title}</h3>
                      </div>
                    </div>

                    <p className="text-xl text-gray-600 leading-relaxed mb-8">
                      {feature.description}
                    </p>

                    <p className="text-gray-500 leading-relaxed">
                      {feature.details}
                    </p>
                  </motion.div>

                  {/* Metrics */}
                  <motion.div
                    className="grid grid-cols-3 gap-6 mb-12"
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    viewport={{ once: true }}
                  >
                    {feature.metrics.map((metric, metricIndex) => (
                      <motion.div
                        key={metricIndex}
                        className="text-center p-4 bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow"
                        whileHover={{ scale: 1.05 }}
                      >
                        <div className="text-lg font-bold text-gray-900 mb-1">{metric}</div>
                        <div className="text-sm text-gray-500">Key Metric</div>
                      </motion.div>
                    ))}
                  </motion.div>

                  {/* Interactive Demo Area */}
                  <motion.div
                    className={`relative h-80 bg-gradient-to-br ${feature.bgColor} rounded-3xl border border-gray-200 overflow-hidden shadow-xl`}
                    initial={{ opacity: 0, scale: 0.9 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.8, delay: 0.5 }}
                    viewport={{ once: true }}
                  >
                    {/* Background Pattern */}
                    <motion.div
                      className="absolute inset-0 opacity-10"
                      animate={{
                        backgroundPosition: ["0% 0%", "100% 100%"],
                      }}
                      transition={{
                        duration: 20,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                      style={{
                        backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%234F46E5' fill-opacity='0.1'%3E%3Cpath d='M20 20c0-11.046-8.954-20-20-20v20h20z'/%3E%3C/g%3E%3C/svg%3E")`,
                        backgroundSize: "40px 40px"
                      }}
                    />

                    {/* Central Feature Visualization */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <motion.div
                        className={`w-32 h-32 bg-gradient-to-r ${feature.color} rounded-3xl flex items-center justify-center shadow-2xl`}
                        animate={{
                          y: [-10, 10, -10],
                          rotate: [0, 5, -5, 0],
                          scale: [1, 1.05, 1]
                        }}
                        transition={{
                          duration: 6,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      >
                        <feature.icon className="w-16 h-16 text-white" />
                      </motion.div>
                    </div>

                    {/* Floating Elements */}
                    {[...Array(6)].map((_, i) => (
                      <motion.div
                        key={i}
                        className={`absolute w-4 h-4 bg-gradient-to-r ${feature.color} rounded-full opacity-40`}
                        style={{
                          left: `${15 + (i % 3) * 30}%`,
                          top: `${20 + Math.floor(i / 3) * 40}%`,
                        }}
                        animate={{
                          y: [-15, 15, -15],
                          opacity: [0.2, 0.8, 0.2],
                          scale: [0.8, 1.2, 0.8]
                        }}
                        transition={{
                          duration: 4,
                          repeat: Infinity,
                          delay: i * 0.3,
                          ease: "easeInOut"
                        }}
                      />
                    ))}

                    {/* Interactive Overlay */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-8"
                      whileHover={{ opacity: 1 }}
                    >
                      <motion.button
                        className="bg-white/90 backdrop-blur-sm text-gray-900 px-6 py-3 rounded-xl font-semibold shadow-lg hover:bg-white transition-colors flex items-center"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <Play className="w-4 h-4 mr-2" />
                        Try Interactive Demo
                      </motion.button>
                    </motion.div>
                  </motion.div>

                  {/* Action Buttons */}
                  <motion.div
                    className="flex flex-col sm:flex-row gap-4 mt-12"
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.6 }}
                    viewport={{ once: true }}
                  >
                    <motion.button
                      className={`flex-1 bg-gradient-to-r ${feature.color} text-white px-8 py-4 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center`}
                      whileHover={{ scale: 1.02, y: -2 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Play className="w-5 h-5 mr-2" />
                      Learn More
                    </motion.button>

                    <motion.button
                      className="flex-1 bg-white text-gray-900 px-8 py-4 rounded-xl font-semibold border border-gray-200 hover:border-gray-300 shadow-sm hover:shadow-md transition-all duration-300 flex items-center justify-center"
                      whileHover={{ scale: 1.02, y: -2 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <ArrowRight className="w-5 h-5 mr-2" />
                      View Documentation
                    </motion.button>
                  </motion.div>
                </div>
              </motion.div>
            ))}

            {/* Final CTA Section */}
            <motion.div
              className="min-h-screen flex items-center justify-center text-center"
              initial={{ opacity: 0, y: 100 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="max-w-2xl">
                <motion.h3
                  className="text-4xl lg:text-5xl font-bold text-gray-900 mb-8"
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  viewport={{ once: true }}
                >
                  Ready to transform your
                  <span className="block bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    workflow?
                  </span>
                </motion.h3>

                <motion.p
                  className="text-xl text-gray-600 mb-12 leading-relaxed"
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  viewport={{ once: true }}
                >
                  Join thousands of teams already using our platform to streamline their operations and accelerate growth.
                </motion.p>

                <motion.div
                  className="flex flex-col sm:flex-row gap-4 justify-center"
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  viewport={{ once: true }}
                >
                  <motion.button
                    className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-10 py-4 rounded-xl font-semibold text-lg shadow-xl hover:shadow-2xl transition-all duration-300"
                    whileHover={{
                      scale: 1.05,
                      boxShadow: "0 25px 50px rgba(59, 130, 246, 0.3)"
                    }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Start Free Trial
                  </motion.button>

                  <motion.button
                    className="bg-white text-gray-900 px-10 py-4 rounded-xl font-semibold text-lg border border-gray-200 hover:border-gray-300 shadow-lg hover:shadow-xl transition-all duration-300"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Schedule Demo
                  </motion.button>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Features
