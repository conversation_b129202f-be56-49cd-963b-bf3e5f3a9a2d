{"hash": "2c8b361a", "configHash": "f8d42e73", "lockfileHash": "4295477f", "browserHash": "e4ed0eaf", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "fab6218e", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "33304584", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "eef02c40", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "9accfb20", "needsInterop": true}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "0efab4b4", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "1eef8c56", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "08d30313", "needsInterop": true}, "react-intersection-observer": {"src": "../../react-intersection-observer/dist/index.mjs", "file": "react-intersection-observer.js", "fileHash": "cc399e37", "needsInterop": false}}, "chunks": {"chunk-S3Z6QACX": {"file": "chunk-S3Z6QACX.js"}, "chunk-2ZET3HRN": {"file": "chunk-2ZET3HRN.js"}, "chunk-IYDKXRZQ": {"file": "chunk-IYDKXRZQ.js"}}}